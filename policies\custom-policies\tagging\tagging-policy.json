{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "ewh-tagging-policy", "properties": {"displayName": "EWH Tagging Policy", "description": "Enforces required tags on resources in EWH Landing Zone", "policyType": "Custom", "mode": "All", "metadata": {"category": "Tags", "version": "1.0.0"}, "parameters": {"requiredTags": {"type": "Array", "metadata": {"displayName": "Required Tags", "description": "List of required tags"}, "defaultValue": ["Environment", "CostCenter", "Owner", "Project"]}, "effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "notIn": ["Microsoft.Resources/subscriptions", "Microsoft.Resources/resourceGroups"]}, {"anyOf": [{"field": "[concat('tags[', parameters('requiredTags')[0], ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('requiredTags')[1], ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('requiredTags')[2], ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('requiredTags')[3], ']')]", "exists": "false"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}