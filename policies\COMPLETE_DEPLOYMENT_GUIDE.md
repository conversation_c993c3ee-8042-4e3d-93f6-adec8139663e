# EWH Landing Zone Complete Policy Deployment Guide

## Overview

This guide provides instructions for deploying a complete Azure Policy solution for the EWH Landing Zone, including custom policies, initiatives, and automatic assignment to management groups.

## What Gets Deployed

### Custom Policies
1. **Enforce EWH Landing Zone Naming Convention** - General naming convention
2. **Enforce EWH Landing Zone Naming Convention - Databases** - Database-specific naming
3. **Enforce EWH Landing Zone Naming Convention - Containers** - Container-specific naming
4. **EWH Tagging Policy** - Enforces required tags (Environment, CostCenter, Owner, Project)
5. **Block SSH/RDP access from Internet (Custom)** - Custom security policy

### Initiative
- **EWH Landing Zone Naming Convention Initiative** - Combines all 3 naming convention policies

### Built-in Policy Assignments
1. **Block SSH/RDP access from Internet to Virtual Machines**
2. **Allowed Locations** - Restricts resource deployment to specified regions
3. **Secure transfer to storage accounts should be enabled**
4. **Key vaults should have deletion protection enabled**
5. **Configure your Storage account public access to be disallowed**

### Management Group Assignments
All policies and initiatives are automatically assigned to:
- **LandingZone** - Main landing zone management group
- **ldz-prd** - Production landing zone
- **ldz-non-prd** - Non-production landing zone
- **Sandbox** - Sandbox environment

## Prerequisites

1. **Azure PowerShell Modules**:
   ```powershell
   Install-Module -Name Az.Accounts, Az.Profile, Az.Resources -Force
   ```

2. **Azure Authentication**:
   ```powershell
   Connect-AzAccount
   ```

3. **Required Permissions**:
   - **Tenant Root Group** - Policy Contributor or Owner
   - **Management Groups** - Management Group Contributor or Owner
   - **Subscriptions** - Policy Contributor (for policy assignments)

4. **Management Groups Must Exist**:
   - Ensure the target management groups are already created
   - Run the management group deployment first if needed

## Quick Deployment

### Single Command Deployment

```powershell
# Navigate to policies directory
cd policies

# Deploy everything with default settings
.\Deploy-Policies-Complete.ps1 -enterpriseScaleCompanyPrefix "EWH"
```

### Custom Deployment

```powershell
# Deploy with custom settings
.\Deploy-Policies-Complete.ps1 `
    -enterpriseScaleCompanyPrefix "EWH" `
    -allowedLocations @("Southeast Asia", "East Asia", "Australia East") `
    -policyEffect "Deny" `
    -location "Southeast Asia" `
    -requiredTags @("Environment", "CostCenter", "Owner", "Project", "Application")
```

## Manual Deployment Steps

If you prefer to deploy step by step:

### Step 1: Deploy Policy Definitions and Initiatives

```powershell
# Deploy policies and initiatives to tenant
New-AzTenantDeployment `
    -Name "deploy-ewh-policies-complete" `
    -Location "Southeast Asia" `
    -TemplateFile "deploy-policies-complete.json" `
    -TemplateParameterFile "deploy-policies-complete.parameters.json"
```

### Step 2: Deploy Policy Assignments

```powershell
# Deploy to each management group
$managementGroups = @("LandingZone", "ldz-prd", "ldz-non-prd", "Sandbox")

foreach ($mg in $managementGroups) {
    New-AzManagementGroupDeployment `
        -Name "deploy-policy-assignments-$mg" `
        -ManagementGroupId $mg `
        -Location "Southeast Asia" `
        -TemplateFile "deploy-policy-assignments-complete.json" `
        -TemplateParameterFile "deploy-policy-assignments-complete.parameters.json"
}
```

## Configuration Parameters

### Template Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enterpriseScaleCompanyPrefix` | string | Required | Company prefix for management groups (max 10 chars) |
| `allowedLocations` | array | ["Southeast Asia", "East Asia"] | Allowed Azure regions |
| `policyEffect` | string | "Audit" | Default policy effect (Audit/Deny/Disabled) |
| `requiredTags` | array | ["Environment", "CostCenter", "Owner", "Project"] | Required resource tags |

### PowerShell Script Parameters

```powershell
.\Deploy-Policies-Complete.ps1 `
    -enterpriseScaleCompanyPrefix "EWH" `           # Required
    -allowedLocations @("Southeast Asia") `         # Optional
    -policyEffect "Audit" `                         # Optional
    -location "Southeast Asia" `                    # Optional
    -requiredTags @("Environment", "Owner")         # Optional
```

## Deployment Process

The script performs the following steps:

1. **Validation**
   - Checks Azure login status
   - Validates template files exist
   - Displays deployment parameters

2. **Policy Definitions Deployment**
   - Deploys custom policy definitions to tenant
   - Creates the naming convention initiative
   - Waits for propagation (30 seconds)

3. **Policy Assignments Deployment**
   - Assigns policies to each management group
   - Handles errors gracefully
   - Continues with remaining groups if one fails

4. **Summary Report**
   - Shows deployment status
   - Lists all deployed policies
   - Indicates which management groups received assignments

## Verification

### Check Policy Definitions

```powershell
# List custom policies
Get-AzPolicyDefinition | Where-Object {$_.Properties.PolicyType -eq "Custom"}

# Check specific policy
Get-AzPolicyDefinition -Name "enforce-ewh-naming-convention"
```

### Check Initiative

```powershell
# Check initiative
Get-AzPolicySetDefinition -Name "ewh-naming-convention-initiative"
```

### Check Policy Assignments

```powershell
# Check assignments for a management group
Get-AzPolicyAssignment -Scope "/providers/Microsoft.Management/managementGroups/LandingZone"

# Check all assignments
Get-AzPolicyAssignment | Where-Object {$_.Properties.DisplayName -like "*EWH*"}
```

### Check Compliance

```powershell
# Get compliance summary
Get-AzPolicyStateSummary -ManagementGroupName "LandingZone"

# Get detailed compliance
Get-AzPolicyState -ManagementGroupName "LandingZone" | Select-Object ResourceId, PolicyDefinitionName, ComplianceState
```

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```
   Error: Insufficient privileges to complete the operation
   ```
   **Solution**: Ensure you have Policy Contributor role at tenant and management group levels

2. **Management Group Not Found**
   ```
   Error: Management group 'LandingZone' not found
   ```
   **Solution**: Deploy management groups first or update the management group names

3. **Policy Assignment Conflicts**
   ```
   Error: Policy assignment already exists
   ```
   **Solution**: Remove existing assignments or use different names

4. **Template Validation Errors**
   ```
   Error: Template validation failed
   ```
   **Solution**: Check JSON syntax and parameter values

### Debugging Commands

```powershell
# Check deployment status
Get-AzTenantDeployment -Name "deploy-ewh-policies-complete"

# Check management group deployment
Get-AzManagementGroupDeployment -ManagementGroupId "LandingZone"

# View deployment operations
Get-AzTenantDeploymentOperation -DeploymentName "deploy-ewh-policies-complete"
```

## Customization

### Adding New Policies

1. Add policy definition to `deploy-policies-complete.json`
2. Add policy assignment to `deploy-policy-assignments-complete.json`
3. Update parameter files if needed
4. Redeploy using the PowerShell script

### Modifying Management Groups

Update the `$managementGroups` array in the PowerShell script:

```powershell
$managementGroups = @(
    @{ Name = "YourMG1"; DisplayName = "Your Management Group 1" },
    @{ Name = "YourMG2"; DisplayName = "Your Management Group 2" }
)
```

## Best Practices

1. **Start with Audit Mode**: Deploy policies with "Audit" effect first
2. **Test in Non-Production**: Test policy assignments in sandbox/dev environments
3. **Gradual Rollout**: Move from Audit → Deny gradually
4. **Monitor Compliance**: Regularly check compliance reports
5. **Document Exemptions**: Maintain clear documentation for any policy exemptions

## Next Steps

After successful deployment:

1. **Monitor Compliance**: Set up Azure Policy compliance dashboards
2. **Create Exemptions**: Document and create necessary policy exemptions
3. **Update Effects**: Gradually change policy effects from Audit to Deny
4. **Expand Coverage**: Add more policies as governance requirements evolve
5. **Automate Reporting**: Set up automated compliance reporting
