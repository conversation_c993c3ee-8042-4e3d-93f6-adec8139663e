{"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "name": "ewh-naming-convention-initiative", "properties": {"displayName": "EWH Landing Zone Naming Convention Initiative", "description": "Initiative containing all EWH naming convention policies", "policyType": "Custom", "metadata": {"category": "Naming", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policies"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyDefinitions": [{"policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'enforce-ewh-naming-convention')]", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "policyDefinitionReferenceId": "enforce-ewh-naming-convention"}, {"policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'enforce-ewh-naming-convention-databases')]", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "policyDefinitionReferenceId": "enforce-ewh-naming-convention-databases"}, {"policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'enforce-ewh-naming-convention-containers')]", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "policyDefinitionReferenceId": "enforce-ewh-naming-convention-containers"}]}}