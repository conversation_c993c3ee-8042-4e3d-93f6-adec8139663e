#Requires -Version 5.1
#Requires -Modules Az.Accounts, Az.Profile, Az.Resources

<#
.SYNOPSIS
    Deploy Azure Policies and Initiatives for EWH Landing Zone

.DESCRIPTION
    This script deploys custom and built-in Azure policies and initiatives to the EWH Landing Zone management groups.

.PARAMETER enterpriseScaleCompanyPrefix
    Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy.

.PARAMETER allowedLocations
    List of allowed locations for resources. Default: Southeast Asia, East Asia

.PARAMETER policyEffect
    Default effect for policies. Default: Audit

.PARAMETER location
    Location for the deployment. Default: Southeast Asia

.EXAMPLE
    .\Deploy-Policies.ps1 -enterpriseScaleCompanyPrefix "EWH" -location "Southeast Asia"

.EXAMPLE
    .\Deploy-Policies.ps1 -enterpriseScaleCompanyPrefix "EWH" -allowedLocations @("Southeast Asia", "East Asia") -policyEffect "Deny"
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$enterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [string[]]$allowedLocations = @("Southeast Asia", "East Asia"),

    [Parameter(Mandatory = $false)]
    [ValidateSet("Audit", "Deny", "Disabled")]
    [string]$policyEffect = "Audit",

    [Parameter(Mandatory = $false)]
    [string]$location = "Southeast Asia"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to check if user is logged in to Azure
function Test-AzureLogin {
    try {
        $context = Get-AzContext
        if ($null -eq $context) {
            return $false
        }
        return $true
    }
    catch {
        return $false
    }
}

# Main script execution
try {
    Write-ColorOutput "Starting EWH Landing Zone Policy Deployment..." "Green"
    Write-ColorOutput "Company Prefix: $enterpriseScaleCompanyPrefix" "Yellow"
    Write-ColorOutput "Allowed Locations: $($allowedLocations -join ', ')" "Yellow"
    Write-ColorOutput "Policy Effect: $policyEffect" "Yellow"
    Write-ColorOutput "Location: $location" "Yellow"

    # Check if user is logged in to Azure
    if (-not (Test-AzureLogin)) {
        Write-ColorOutput "Not logged in to Azure. Please run Connect-AzAccount first." "Red"
        exit 1
    }

    # Get current context
    $context = Get-AzContext
    Write-ColorOutput "Logged in as: $($context.Account.Id)" "Green"
    Write-ColorOutput "Tenant ID: $($context.Tenant.Id)" "Green"

    # Step 1: Deploy Policy Definitions
    Write-ColorOutput "`nStep 1: Deploying Policy Definitions..." "Cyan"
    
    $policyDeploymentName = "deploy-ewh-policies-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $policyTemplateFile = Join-Path $PSScriptRoot "deploy-policies.json"
    
    if (-not (Test-Path $policyTemplateFile)) {
        Write-ColorOutput "Policy template file not found: $policyTemplateFile" "Red"
        exit 1
    }

    $policyParameters = @{
        enterpriseScaleCompanyPrefix = $enterpriseScaleCompanyPrefix
        allowedLocations = $allowedLocations
        policyEffect = $policyEffect
    }

    $policyDeployment = New-AzTenantDeployment `
        -Name $policyDeploymentName `
        -Location $location `
        -TemplateFile $policyTemplateFile `
        -TemplateParameterObject $policyParameters `
        -Verbose

    if ($policyDeployment.ProvisioningState -eq "Succeeded") {
        Write-ColorOutput "Policy definitions deployed successfully!" "Green"
    } else {
        Write-ColorOutput "Policy definitions deployment failed!" "Red"
        exit 1
    }

    # Step 2: Deploy Policy Assignments
    Write-ColorOutput "`nStep 2: Deploying Policy Assignments..." "Cyan"
    
    $assignmentDeploymentName = "deploy-ewh-policy-assignments-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $assignmentTemplateFile = Join-Path $PSScriptRoot "assignments\deploy-policy-assignments.json"
    
    if (-not (Test-Path $assignmentTemplateFile)) {
        Write-ColorOutput "Policy assignment template file not found: $assignmentTemplateFile" "Red"
        exit 1
    }

    $assignmentParameters = @{
        enterpriseScaleCompanyPrefix = $enterpriseScaleCompanyPrefix
        allowedLocations = $allowedLocations
        policyEffect = $policyEffect
    }

    # Deploy to the Landing Zone management group
    $managementGroupId = "LandingZone"
    
    $assignmentDeployment = New-AzManagementGroupDeployment `
        -Name $assignmentDeploymentName `
        -ManagementGroupId $managementGroupId `
        -Location $location `
        -TemplateFile $assignmentTemplateFile `
        -TemplateParameterObject $assignmentParameters `
        -Verbose

    if ($assignmentDeployment.ProvisioningState -eq "Succeeded") {
        Write-ColorOutput "Policy assignments deployed successfully!" "Green"
    } else {
        Write-ColorOutput "Policy assignments deployment failed!" "Red"
        exit 1
    }

    Write-ColorOutput "`nEWH Landing Zone Policy Deployment completed successfully!" "Green"
    Write-ColorOutput "Deployment Summary:" "Yellow"
    Write-ColorOutput "- Policy Definitions: $($policyDeployment.ProvisioningState)" "White"
    Write-ColorOutput "- Policy Assignments: $($assignmentDeployment.ProvisioningState)" "White"

} catch {
    Write-ColorOutput "An error occurred during deployment: $($_.Exception.Message)" "Red"
    Write-ColorOutput "Stack Trace: $($_.ScriptStackTrace)" "Red"
    exit 1
}
