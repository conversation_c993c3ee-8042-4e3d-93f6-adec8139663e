#Requires -Version 5.1
#Requires -Modules Az.Accounts, Az.Profile, Az.Resources

<#
.SYNOPSIS
    Deploy Azure Policies, Initiatives and Assignments for EWH Landing Zone

.DESCRIPTION
    This script deploys custom and built-in Azure policies, initiatives and assigns them to the EWH Landing Zone management groups.

.PARAMETER enterpriseScaleCompanyPrefix
    Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy.

.PARAMETER allowedLocations
    List of allowed locations for resources. Default: Southeast Asia, East Asia

.PARAMETER policyEffect
    Default effect for policies. Default: Audit

.PARAMETER location
    Location for the deployment. Default: Southeast Asia

.PARAMETER requiredTags
    List of required tags for resources. Default: Environment, CostCenter, Owner, Project

.EXAMPLE
    .\Deploy-Policies-Complete.ps1 -enterpriseScaleCompanyPrefix "EWH" -location "Southeast Asia"

.EXAMPLE
    .\Deploy-Policies-Complete.ps1 -enterpriseScaleCompanyPrefix "EWH" -allowedLocations @("Southeast Asia", "East Asia") -policyEffect "Deny"
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$enterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [string[]]$allowedLocations = @("Southeast Asia", "East Asia"),

    [Parameter(Mandatory = $false)]
    [ValidateSet("Audit", "Deny", "Disabled")]
    [string]$policyEffect = "Audit",

    [Parameter(Mandatory = $false)]
    [string]$location = "Southeast Asia",

    [Parameter(Mandatory = $false)]
    [string[]]$requiredTags = @("Environment", "CostCenter", "Owner", "Project")
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to check if user is logged in to Azure
function Test-AzureLogin {
    try {
        $context = Get-AzContext
        if ($null -eq $context) {
            return $false
        }
        return $true
    }
    catch {
        return $false
    }
}

# Main script execution
try {
    Write-ColorOutput "Starting EWH Landing Zone Complete Policy Deployment..." "Green"
    Write-ColorOutput "Company Prefix: $enterpriseScaleCompanyPrefix" "Yellow"
    Write-ColorOutput "Allowed Locations: $($allowedLocations -join ', ')" "Yellow"
    Write-ColorOutput "Policy Effect: $policyEffect" "Yellow"
    Write-ColorOutput "Location: $location" "Yellow"
    Write-ColorOutput "Required Tags: $($requiredTags -join ', ')" "Yellow"

    # Check if user is logged in to Azure
    if (-not (Test-AzureLogin)) {
        Write-ColorOutput "Not logged in to Azure. Please run Connect-AzAccount first." "Red"
        exit 1
    }

    # Get current context
    $context = Get-AzContext
    Write-ColorOutput "Logged in as: $($context.Account.Id)" "Green"
    Write-ColorOutput "Tenant ID: $($context.Tenant.Id)" "Green"

    # Step 1: Deploy Policy Definitions and Initiatives
    Write-ColorOutput "`nStep 1: Deploying Policy Definitions and Initiatives..." "Cyan"
    
    $policyDeploymentName = "deploy-ewh-policies-complete-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $policyTemplateFile = Join-Path $PSScriptRoot "deploy-policies-complete.json"
    
    if (-not (Test-Path $policyTemplateFile)) {
        Write-ColorOutput "Policy template file not found: $policyTemplateFile" "Red"
        exit 1
    }

    $policyParameters = @{
        enterpriseScaleCompanyPrefix = $enterpriseScaleCompanyPrefix
        allowedLocations = $allowedLocations
        policyEffect = $policyEffect
        requiredTags = $requiredTags
    }

    Write-ColorOutput "Deploying policy definitions and initiatives..." "Yellow"
    $policyDeployment = New-AzTenantDeployment `
        -Name $policyDeploymentName `
        -Location $location `
        -TemplateFile $policyTemplateFile `
        -TemplateParameterObject $policyParameters `
        -Verbose

    if ($policyDeployment.ProvisioningState -eq "Succeeded") {
        Write-ColorOutput "Policy definitions and initiatives deployed successfully!" "Green"
    } else {
        Write-ColorOutput "Policy definitions and initiatives deployment failed!" "Red"
        Write-ColorOutput "Deployment State: $($policyDeployment.ProvisioningState)" "Red"
        exit 1
    }

    # Wait a bit for policies to propagate
    Write-ColorOutput "Waiting for policies to propagate..." "Yellow"
    Start-Sleep -Seconds 30

    # Step 2: Deploy Policy Assignments to Management Groups
    Write-ColorOutput "`nStep 2: Deploying Policy Assignments to Management Groups..." "Cyan"
    
    $assignmentDeploymentName = "deploy-ewh-policy-assignments-complete-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $assignmentTemplateFile = Join-Path $PSScriptRoot "deploy-policy-assignments-complete.json"
    
    if (-not (Test-Path $assignmentTemplateFile)) {
        Write-ColorOutput "Policy assignment template file not found: $assignmentTemplateFile" "Red"
        exit 1
    }

    $assignmentParameters = @{
        enterpriseScaleCompanyPrefix = $enterpriseScaleCompanyPrefix
        allowedLocations = $allowedLocations
        policyEffect = $policyEffect
        requiredTags = $requiredTags
    }

    # Deploy to different management groups
    $managementGroups = @(
        @{ Name = "LandingZone"; DisplayName = "Landing Zone" },
        @{ Name = "ldz-prd"; DisplayName = "Production Landing Zone" },
        @{ Name = "ldz-non-prd"; DisplayName = "Non-Production Landing Zone" },
        @{ Name = "Sandbox"; DisplayName = "Sandbox" }
    )

    $successfulAssignments = 0
    $totalAssignments = $managementGroups.Count

    foreach ($mg in $managementGroups) {
        try {
            Write-ColorOutput "Deploying to Management Group: $($mg.DisplayName) ($($mg.Name))" "Yellow"
            
            $mgAssignmentDeployment = New-AzManagementGroupDeployment `
                -Name "$assignmentDeploymentName-$($mg.Name)" `
                -ManagementGroupId $mg.Name `
                -Location $location `
                -TemplateFile $assignmentTemplateFile `
                -TemplateParameterObject $assignmentParameters `
                -Verbose

            if ($mgAssignmentDeployment.ProvisioningState -eq "Succeeded") {
                Write-ColorOutput "✓ Policy assignments deployed successfully to $($mg.DisplayName)!" "Green"
                $successfulAssignments++
            } else {
                Write-ColorOutput "✗ Policy assignments deployment failed for $($mg.DisplayName)!" "Red"
                Write-ColorOutput "  Deployment State: $($mgAssignmentDeployment.ProvisioningState)" "Red"
            }
        } catch {
            Write-ColorOutput "✗ Error deploying to $($mg.DisplayName): $($_.Exception.Message)" "Red"
            # Continue with other management groups
        }
    }

    # Final Summary
    Write-ColorOutput "`n=== EWH Landing Zone Policy Deployment Summary ===" "Cyan"
    Write-ColorOutput "Policy Definitions & Initiatives: $($policyDeployment.ProvisioningState)" "White"
    Write-ColorOutput "Policy Assignments: $successfulAssignments/$totalAssignments management groups" "White"

    if ($policyDeployment.ProvisioningState -eq "Succeeded" -and $successfulAssignments -eq $totalAssignments) {
        Write-ColorOutput "`n🎉 Complete deployment successful!" "Green"
        
        Write-ColorOutput "`nDeployed Custom Policies:" "Yellow"
        Write-ColorOutput "• Enforce EWH Landing Zone Naming Convention" "White"
        Write-ColorOutput "• Enforce EWH Landing Zone Naming Convention - Databases" "White"
        Write-ColorOutput "• Enforce EWH Landing Zone Naming Convention - Containers" "White"
        Write-ColorOutput "• EWH Tagging Policy" "White"
        Write-ColorOutput "• Block SSH/RDP access from Internet (Custom)" "White"
        
        Write-ColorOutput "`nDeployed Initiative:" "Yellow"
        Write-ColorOutput "• EWH Landing Zone Naming Convention Initiative" "White"
        
        Write-ColorOutput "`nAssigned Built-in Policies:" "Yellow"
        Write-ColorOutput "• Block SSH/RDP access from Internet to Virtual Machines" "White"
        Write-ColorOutput "• Allowed Locations" "White"
        Write-ColorOutput "• Secure transfer to storage accounts should be enabled" "White"
        Write-ColorOutput "• Key vaults should have deletion protection enabled" "White"
        Write-ColorOutput "• Configure your Storage account public access to be disallowed" "White"
        
        Write-ColorOutput "`nManagement Groups with Policy Assignments:" "Yellow"
        foreach ($mg in $managementGroups) {
            Write-ColorOutput "• $($mg.DisplayName)" "White"
        }
        
    } elseif ($policyDeployment.ProvisioningState -eq "Succeeded") {
        Write-ColorOutput "`n⚠️ Policies deployed but some assignments failed!" "Yellow"
        Write-ColorOutput "Check the errors above and retry failed assignments." "Yellow"
    } else {
        Write-ColorOutput "`n❌ Deployment failed!" "Red"
        exit 1
    }

} catch {
    Write-ColorOutput "An error occurred during deployment: $($_.Exception.Message)" "Red"
    Write-ColorOutput "Stack Trace: $($_.ScriptStackTrace)" "Red"
    exit 1
}
