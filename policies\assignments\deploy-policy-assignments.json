{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Deploy Policy Assignments for EWH Landing Zone Management Groups"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "allowedLocations": {"type": "array", "defaultValue": ["Southeast Asia", "East Asia"], "metadata": {"description": "List of allowed locations for resources"}}, "policyEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"description": "Default effect for policies"}}}, "variables": {"mgmtGroups": {"eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "LandingZone", "ldzPrd": "ldz-prd", "ldzNonPrd": "ldz-non-prd", "sandboxes": "Sandbox"}, "builtInPolicyIds": {"blockSshRdpInternet": "/providers/Microsoft.Authorization/policyDefinitions/2c89a2e5-7285-40fe-afe0-ae8654b92fab", "allowedLocations": "/providers/Microsoft.Authorization/policyDefinitions/e56962a6-4747-49cd-b67b-bf8b01975c4c", "secureTransferStorage": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "keyVaultDeletionProtection": "/providers/Microsoft.Authorization/policyDefinitions/0b60c0b2-2dc2-4e1c-b5c9-abbed971de53", "storagePublicAccess": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751"}}, "resources": [{"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "name": "ewh-naming-convention-initiative", "properties": {"displayName": "EWH Landing Zone Naming Convention Initiative", "description": "Initiative containing all EWH naming convention policies", "policyType": "Custom", "metadata": {"category": "Naming", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policies"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyDefinitions": [{"policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'enforce-ewh-naming-convention')]", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "policyDefinitionReferenceId": "enforce-ewh-naming-convention"}, {"policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'enforce-ewh-naming-convention-databases')]", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "policyDefinitionReferenceId": "enforce-ewh-naming-convention-databases"}, {"policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'enforce-ewh-naming-convention-containers')]", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "policyDefinitionReferenceId": "enforce-ewh-naming-convention-containers"}]}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-ewh-naming-convention-initiative", "dependsOn": ["[resourceId('Microsoft.Authorization/policySetDefinitions', 'ewh-naming-convention-initiative')]"], "properties": {"displayName": "Assign EWH Naming Convention Initiative", "description": "Assignment of EWH naming convention initiative to Landing Zone management group", "policyDefinitionId": "[resourceId('Microsoft.Authorization/policySetDefinitions', 'ewh-naming-convention-initiative')]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "[parameters('policyEffect')]"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-ewh-tagging-policy", "properties": {"displayName": "Assign EWH Tagging Policy", "description": "Assignment of EWH tagging policy to Landing Zone management group", "policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'ewh-tagging-policy')]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "[parameters('policyEffect')]"}, "requiredTags": {"value": ["Environment", "CostCenter", "Owner", "Project"]}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-block-ssh-rdp-builtin", "properties": {"displayName": "Block SSH/RDP access from Internet (Built-in)", "description": "Assignment of built-in policy to block SSH/RDP access from Internet", "policyDefinitionId": "[variables('builtInPolicyIds').blockSshRdpInternet]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-allowed-locations", "properties": {"displayName": "Allowed Locations", "description": "Assignment of allowed locations policy", "policyDefinitionId": "[variables('builtInPolicyIds').allowedLocations]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"listOfAllowedLocations": {"value": "[parameters('allowedLocations')]"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-secure-transfer-storage", "properties": {"displayName": "Secure transfer to storage accounts should be enabled", "description": "Assignment of secure transfer to storage accounts policy", "policyDefinitionId": "[variables('builtInPolicyIds').secureTransferStorage]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "Audit"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-keyvault-deletion-protection", "properties": {"displayName": "Key vaults should have deletion protection enabled", "description": "Assignment of key vault deletion protection policy", "policyDefinitionId": "[variables('builtInPolicyIds').keyVaultDeletionProtection]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "Audit"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-storage-public-access", "properties": {"displayName": "Configure your Storage account public access to be disallowed", "description": "Assignment of storage account public access policy", "policyDefinitionId": "[variables('builtInPolicyIds').storagePublicAccess]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "Modify"}}}}]}