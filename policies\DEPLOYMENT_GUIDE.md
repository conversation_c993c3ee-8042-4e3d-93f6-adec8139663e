# EWH Landing Zone Policy Deployment Guide

## Overview

This guide provides instructions for deploying Azure Policies and Initiatives for the EWH Landing Zone implementation. The deployment includes both custom policies specific to EWH requirements and built-in Azure policies for security and compliance.

## Prerequisites

1. **Azure PowerShell Modules**:
   ```powershell
   Install-Module -Name Az.Accounts, Az.Profile, Az.Resources -Force
   ```

2. **Azure Authentication**:
   ```powershell
   Connect-AzAccount
   ```

3. **Required Permissions**:
   - Management Group Contributor or Owner role
   - Policy Contributor role at the tenant level

## Deployment Structure

### Custom Policies

1. **Enforce EWH Landing Zone Naming Convention**
   - General naming convention for resources
   - File: `custom-policies/naming-convention/enforce-ewh-naming-convention.json`

2. **Enforce EWH Landing Zone Naming Convention - Databases**
   - Specific naming for database resources
   - File: `custom-policies/naming-convention/enforce-ewh-naming-convention-databases.json`

3. **Enforce EWH Landing Zone Naming Convention - Containers**
   - Specific naming for container resources
   - File: `custom-policies/naming-convention/enforce-ewh-naming-convention-containers.json`

4. **EWH Tagging Policy**
   - Enforces required tags: Environment, CostCenter, Owner, Project
   - File: `custom-policies/tagging/tagging-policy.json`

5. **Block SSH/RDP access from Internet (Custom)**
   - Custom security policy
   - File: `custom-policies/security/block-ssh-rdp-internet.json`

### Built-in Policies

1. **Block SSH/RDP access from Internet to Virtual Machines**
   - Policy ID: `2c89a2e5-7285-40fe-afe0-ae8654b92fab`

2. **Allowed Locations**
   - Policy ID: `e56962a6-4747-49cd-b67b-bf8b01975c4c`

3. **Secure transfer to storage accounts should be enabled**
   - Policy ID: `404c3081-a854-4457-ae30-26a93ef643f9`

4. **Key vaults should have deletion protection enabled**
   - Policy ID: `0b60c0b2-2dc2-4e1c-b5c9-abbed971de53`

5. **Configure your Storage account public access to be disallowed**
   - Policy ID: `4fa4b6c0-31ca-4c0d-b10d-24b96f62a751`

### Initiative

**EWH Landing Zone Naming Convention Initiative**
- Combines all three naming convention policies
- File: `initiatives/ewh-naming-convention-initiative.json`

## Deployment Methods

### Method 1: PowerShell Script (Recommended)

```powershell
# Navigate to the policies directory
cd policies

# Run the deployment script
.\Deploy-Policies.ps1 -enterpriseScaleCompanyPrefix "EWH" -location "Southeast Asia"

# With custom parameters
.\Deploy-Policies.ps1 `
    -enterpriseScaleCompanyPrefix "EWH" `
    -allowedLocations @("Southeast Asia", "East Asia") `
    -policyEffect "Deny" `
    -location "Southeast Asia"
```

### Method 2: Azure CLI

```bash
# Deploy policy definitions
az deployment tenant create \
    --name "deploy-ewh-policies" \
    --location "Southeast Asia" \
    --template-file "deploy-policies.json" \
    --parameters "deploy-policies.parameters.json"

# Deploy policy assignments
az deployment mg create \
    --name "deploy-ewh-policy-assignments" \
    --management-group-id "LandingZone" \
    --location "Southeast Asia" \
    --template-file "assignments/deploy-policy-assignments.json" \
    --parameters "assignments/deploy-policy-assignments.parameters.json"
```

### Method 3: Azure Portal

1. Navigate to Azure Portal > Management Groups
2. Select the appropriate management group
3. Go to Deployments > Create
4. Upload the template files and configure parameters

## Configuration Parameters

### Required Parameters

- **enterpriseScaleCompanyPrefix**: Company prefix for management group hierarchy (max 10 characters)

### Optional Parameters

- **allowedLocations**: Array of allowed Azure regions (default: ["Southeast Asia", "East Asia"])
- **policyEffect**: Default effect for policies (default: "Audit", options: "Audit", "Deny", "Disabled")
- **location**: Deployment location (default: "Southeast Asia")

## Management Group Assignments

Policies are assigned to the following management groups:

- **Landing Zone**: All policies and initiatives
- **ldz-prd**: Production-specific policies
- **ldz-non-prd**: Non-production-specific policies
- **Sandbox**: Limited policy enforcement

## Post-Deployment Verification

### Verify Policy Definitions

```powershell
# List custom policy definitions
Get-AzPolicyDefinition | Where-Object {$_.Properties.PolicyType -eq "Custom"}

# Check specific policy
Get-AzPolicyDefinition -Name "enforce-ewh-naming-convention"
```

### Verify Policy Assignments

```powershell
# List policy assignments for Landing Zone management group
Get-AzPolicyAssignment -Scope "/providers/Microsoft.Management/managementGroups/LandingZone"
```

### Verify Initiative

```powershell
# Check initiative definition
Get-AzPolicySetDefinition -Name "ewh-naming-convention-initiative"
```

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure you have the required roles at the management group level
   - Check if you're logged in to the correct Azure tenant

2. **Template Validation Errors**
   - Verify JSON syntax in template files
   - Check parameter values match expected formats

3. **Policy Assignment Failures**
   - Ensure policy definitions are deployed before assignments
   - Verify management group IDs exist

### Logs and Monitoring

```powershell
# Check deployment status
Get-AzTenantDeployment -Name "deploy-ewh-policies"
Get-AzManagementGroupDeployment -ManagementGroupId "LandingZone" -Name "deploy-ewh-policy-assignments"

# View deployment operations
Get-AzTenantDeploymentOperation -DeploymentName "deploy-ewh-policies"
```

## Customization

### Adding New Policies

1. Create policy definition JSON file in appropriate subfolder
2. Add policy definition to `deploy-policies.json`
3. Create policy assignment in `assignments/deploy-policy-assignments.json`
4. Update parameters files if needed

### Modifying Existing Policies

1. Update policy definition files
2. Increment version in metadata
3. Redeploy using the PowerShell script

## Maintenance

### Regular Tasks

1. **Review Policy Compliance**: Monitor compliance reports in Azure Portal
2. **Update Policy Effects**: Gradually move from "Audit" to "Deny" for critical policies
3. **Add New Locations**: Update allowed locations as business expands
4. **Review Exemptions**: Regularly audit and clean up policy exemptions

### Version Control

- All policy files are version controlled
- Use semantic versioning for policy definitions
- Document changes in commit messages
