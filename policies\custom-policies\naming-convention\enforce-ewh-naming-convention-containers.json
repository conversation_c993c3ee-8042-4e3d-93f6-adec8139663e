{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "enforce-ewh-naming-convention-containers", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Containers", "description": "Enforces naming convention for container resources in EWH Landing Zone", "policyType": "Custom", "mode": "All", "metadata": {"category": "Naming", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "in": ["Microsoft.ContainerService/managedClusters", "Microsoft.ContainerRegistry/registries", "Microsoft.ContainerInstance/containerGroups"]}]}, "then": {"effect": "[parameters('effect')]"}}}}