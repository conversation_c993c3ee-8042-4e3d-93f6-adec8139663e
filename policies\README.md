# Azure Policy Templates for EWH Landing Zone

This directory contains Azure Policy definitions and initiatives for the EWH Landing Zone implementation.

## Structure

```
policies/
├── custom-policies/           # Custom policy definitions
│   ├── naming-convention/     # Naming convention policies
│   ├── tagging/              # Tagging policies
│   └── security/             # Security policies
├── initiatives/              # Policy initiatives (sets)
├── assignments/              # Policy assignments to management groups
└── deploy-policies.json      # Main deployment template
```

## Custom Policies

### Naming Convention Policies
- **Enforce EWH Landing Zone Naming Convention**: General naming convention for resources
- **Enforce EWH Landing Zone Naming Convention - Databases**: Specific naming for database resources
- **Enforce EWH Landing Zone Naming Convention - Containers**: Specific naming for container resources

### Security Policies
- **Tagging Policy**: Enforce required tags on resources
- **Block SSH/RDP access from Internet to Virtual Machines**: Custom security policy

## Built-in Policies

The following built-in policies are assigned:
- Block SSH/RDP access from Internet to Virtual Machines
- Allowed Locations
- Secure transfer to storage accounts should be enabled
- Key vaults should have deletion protection enabled
- Configure your Storage account public access to be disallowed

## Deployment

Use the `Deploy-Policies.ps1` script to deploy all policies and assignments to the management groups.

```powershell
.\Deploy-Policies.ps1 -enterpriseScaleCompanyPrefix "EWH" -location "Southeast Asia"
```
