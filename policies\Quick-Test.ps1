# Quick test for EWH Landing Zone Policy Templates

Write-Host "=== EWH Landing Zone Policy Templates Test ===" -ForegroundColor Cyan

# Test 1: Check if all required files exist
Write-Host "`n1. File Existence Test:" -ForegroundColor Yellow

$requiredFiles = @(
    "deploy-policies.json",
    "deploy-policies.parameters.json", 
    "Deploy-Policies.ps1",
    "assignments\deploy-policy-assignments.json",
    "assignments\deploy-policy-assignments.parameters.json",
    "initiatives\ewh-naming-convention-initiative.json"
)

$filesExist = 0
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "   ✓ $file" -ForegroundColor Green
        $filesExist++
    } else {
        Write-Host "   ✗ $file" -ForegroundColor Red
    }
}

Write-Host "   Files: $filesExist/$($requiredFiles.Count) found" -ForegroundColor $(if ($filesExist -eq $requiredFiles.Count) { "Green" } else { "Red" })

# Test 2: JSON Validation
Write-Host "`n2. JSON Validation Test:" -ForegroundColor Yellow

$jsonFiles = @(
    "deploy-policies.json",
    "deploy-policies.parameters.json",
    "assignments\deploy-policy-assignments.json",
    "assignments\deploy-policy-assignments.parameters.json"
)

$jsonValid = 0
foreach ($file in $jsonFiles) {
    if (Test-Path $file) {
        try {
            $content = Get-Content $file -Raw | ConvertFrom-Json
            Write-Host "   ✓ $file" -ForegroundColor Green
            $jsonValid++
        } catch {
            Write-Host "   ✗ $file - Invalid JSON" -ForegroundColor Red
        }
    }
}

Write-Host "   JSON: $jsonValid/$($jsonFiles.Count) valid" -ForegroundColor $(if ($jsonValid -eq $jsonFiles.Count) { "Green" } else { "Red" })

# Test 3: Template Structure
Write-Host "`n3. Template Structure Test:" -ForegroundColor Yellow

try {
    $mainTemplate = Get-Content "deploy-policies.json" -Raw | ConvertFrom-Json
    
    # Check required sections
    $hasSchema = $mainTemplate.'$schema' -ne $null
    $hasParameters = $mainTemplate.parameters -ne $null
    $hasResources = $mainTemplate.resources -ne $null
    
    Write-Host "   ✓ Schema: $hasSchema" -ForegroundColor $(if ($hasSchema) { "Green" } else { "Red" })
    Write-Host "   ✓ Parameters: $hasParameters" -ForegroundColor $(if ($hasParameters) { "Green" } else { "Red" })
    Write-Host "   ✓ Resources: $hasResources" -ForegroundColor $(if ($hasResources) { "Green" } else { "Red" })
    
    # Count resources
    $resourceCount = $mainTemplate.resources.Count
    Write-Host "   ✓ Resource count: $resourceCount" -ForegroundColor Green
    
} catch {
    Write-Host "   ✗ Failed to parse main template" -ForegroundColor Red
}

# Test 4: Parameter Validation
Write-Host "`n4. Parameter Validation Test:" -ForegroundColor Yellow

try {
    $params = Get-Content "deploy-policies.parameters.json" -Raw | ConvertFrom-Json
    
    $hasPrefix = $params.parameters.enterpriseScaleCompanyPrefix -ne $null
    $hasLocations = $params.parameters.allowedLocations -ne $null
    $hasEffect = $params.parameters.policyEffect -ne $null
    
    Write-Host "   ✓ Enterprise prefix: $hasPrefix" -ForegroundColor $(if ($hasPrefix) { "Green" } else { "Red" })
    Write-Host "   ✓ Allowed locations: $hasLocations" -ForegroundColor $(if ($hasLocations) { "Green" } else { "Red" })
    Write-Host "   ✓ Policy effect: $hasEffect" -ForegroundColor $(if ($hasEffect) { "Green" } else { "Red" })
    
} catch {
    Write-Host "   ✗ Failed to parse parameters file" -ForegroundColor Red
}

# Test 5: Azure Connection (if available)
Write-Host "`n5. Azure Connection Test:" -ForegroundColor Yellow

try {
    $context = Get-AzContext -ErrorAction SilentlyContinue
    if ($context) {
        Write-Host "   ✓ Connected to Azure" -ForegroundColor Green
        Write-Host "   ✓ Account: $($context.Account.Id)" -ForegroundColor Green
        Write-Host "   ✓ Tenant: $($context.Tenant.Id)" -ForegroundColor Green
        
        # Test template validation
        try {
            $validation = Test-AzDeployment -Location "Southeast Asia" -TemplateFile "deploy-policies.json" -TemplateParameterFile "deploy-policies.parameters.json"
            if ($validation.Count -eq 0) {
                Write-Host "   ✓ Template validation passed" -ForegroundColor Green
            } else {
                Write-Host "   ✗ Template validation failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "   ⚠ Template validation error: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ⚠ Not connected to Azure" -ForegroundColor Yellow
        Write-Host "   ℹ Run 'Connect-AzAccount' to test Azure integration" -ForegroundColor Cyan
    }
} catch {
    Write-Host "   ⚠ Azure PowerShell module not available" -ForegroundColor Yellow
    Write-Host "   ℹ Run 'Install-Module -Name Az -Force' to install" -ForegroundColor Cyan
}

# Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Cyan
Write-Host "Files: $filesExist/$($requiredFiles.Count) exist" -ForegroundColor $(if ($filesExist -eq $requiredFiles.Count) { "Green" } else { "Red" })
Write-Host "JSON: $jsonValid/$($jsonFiles.Count) valid" -ForegroundColor $(if ($jsonValid -eq $jsonFiles.Count) { "Green" } else { "Red" })

$overallScore = $filesExist + $jsonValid
$maxScore = $requiredFiles.Count + $jsonFiles.Count

Write-Host "`nOverall Score: $overallScore/$maxScore" -ForegroundColor $(if ($overallScore -eq $maxScore) { "Green" } else { "Yellow" })

if ($overallScore -eq $maxScore) {
    Write-Host "`n🎉 All core tests passed! Templates are ready for deployment." -ForegroundColor Green
} else {
    Write-Host "`n⚠️ Some tests failed. Please review the results above." -ForegroundColor Yellow
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Connect to Azure: Connect-AzAccount" -ForegroundColor White
Write-Host "2. Deploy policies: .\Deploy-Policies.ps1 -enterpriseScaleCompanyPrefix 'EWH'" -ForegroundColor White
