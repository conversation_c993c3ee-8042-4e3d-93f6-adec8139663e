{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "block-ssh-rdp-internet-custom", "properties": {"displayName": "Block SSH/RDP access from Internet to Virtual Machines (Custom)", "description": "Custom policy to block SSH/RDP access from Internet to Virtual Machines", "policyType": "Custom", "mode": "All", "metadata": {"category": "Security", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups/securityRules"}, {"allOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "in": ["22", "3389"]}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRanges[*]", "in": ["22", "3389"]}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "in": ["*", "Internet"]}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "in": ["*", "Internet"]}]}]}]}, "then": {"effect": "[parameters('effect')]"}}}}