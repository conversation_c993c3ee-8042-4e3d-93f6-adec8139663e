{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Deploy Azure Policies and Initiatives for EWH Landing Zone"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "allowedLocations": {"type": "array", "defaultValue": ["Southeast Asia", "East Asia"], "metadata": {"description": "List of allowed locations for resources"}}, "policyEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"description": "Default effect for policies"}}}, "variables": {"mgmtGroups": {"eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "LandingZone", "ldzPrd": "ldz-prd", "ldzNonPrd": "ldz-non-prd", "sandboxes": "Sandbox"}, "builtInPolicyIds": {"blockSshRdpInternet": "/providers/Microsoft.Authorization/policyDefinitions/2c89a2e5-7285-40fe-afe0-ae8654b92fab", "allowedLocations": "/providers/Microsoft.Authorization/policyDefinitions/e56962a6-4747-49cd-b67b-bf8b01975c4c", "secureTransferStorage": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "keyVaultDeletionProtection": "/providers/Microsoft.Authorization/policyDefinitions/0b60c0b2-2dc2-4e1c-b5c9-abbed971de53", "storagePublicAccess": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751"}}, "resources": [{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "enforce-ewh-naming-convention", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention", "description": "Enforces naming convention for resources in EWH Landing Zone", "policyType": "Custom", "mode": "All", "metadata": {"category": "Naming", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "in": ["Microsoft.Compute/virtualMachines", "Microsoft.Storage/storageAccounts", "Microsoft.Network/virtualNetworks", "Microsoft.Network/networkSecurityGroups"]}]}, "then": {"effect": "[parameters('effect')]"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "enforce-ewh-naming-convention-databases", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Databases", "description": "Enforces naming convention for database resources in EWH Landing Zone", "policyType": "Custom", "mode": "All", "metadata": {"category": "Naming", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "in": ["Microsoft.Sql/servers", "Microsoft.Sql/servers/databases", "Microsoft.DBforMySQL/servers", "Microsoft.DBforPostgreSQL/servers"]}]}, "then": {"effect": "[parameters('effect')]"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "enforce-ewh-naming-convention-containers", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Containers", "description": "Enforces naming convention for container resources in EWH Landing Zone", "policyType": "Custom", "mode": "All", "metadata": {"category": "Naming", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "in": ["Microsoft.ContainerService/managedClusters", "Microsoft.ContainerRegistry/registries", "Microsoft.ContainerInstance/containerGroups"]}]}, "then": {"effect": "[parameters('effect')]"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "ewh-tagging-policy", "properties": {"displayName": "EWH Tagging Policy", "description": "Enforces required tags on resources in EWH Landing Zone", "policyType": "Custom", "mode": "All", "metadata": {"category": "Tags", "version": "1.0.0"}, "parameters": {"requiredTags": {"type": "Array", "metadata": {"displayName": "Required Tags", "description": "List of required tags"}, "defaultValue": ["Environment", "CostCenter", "Owner", "Project"]}, "effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "notIn": ["Microsoft.Resources/subscriptions", "Microsoft.Resources/resourceGroups"]}, {"anyOf": [{"field": "[concat('tags[', parameters('requiredTags')[0], ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('requiredTags')[1], ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('requiredTags')[2], ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('requiredTags')[3], ']')]", "exists": "false"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}]}