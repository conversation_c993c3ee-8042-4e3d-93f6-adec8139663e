{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "enforce-ewh-naming-convention", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention", "description": "Enforces naming convention for resources in EWH Landing Zone", "policyType": "Custom", "mode": "All", "metadata": {"category": "Naming", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "in": ["Microsoft.Compute/virtualMachines", "Microsoft.Storage/storageAccounts", "Microsoft.Network/virtualNetworks", "Microsoft.Network/networkSecurityGroups"]}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Resources/tags", "existenceCondition": {"field": "Microsoft.Resources/tags['Environment']", "exists": "true"}}}}}}