{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Deploy Policy Assignments for EWH Landing Zone Management Groups"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "allowedLocations": {"type": "array", "defaultValue": ["Southeast Asia", "East Asia"], "metadata": {"description": "List of allowed locations for resources"}}, "policyEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"description": "Default effect for policies"}}, "requiredTags": {"type": "array", "defaultValue": ["Environment", "CostCenter", "Owner", "Project"], "metadata": {"description": "List of required tags for resources"}}}, "variables": {"mgmtGroups": {"eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "LandingZone", "ldzPrd": "ldz-prd", "ldzNonPrd": "ldz-non-prd", "sandboxes": "Sandbox"}, "builtInPolicyIds": {"blockSshRdpInternet": "/providers/Microsoft.Authorization/policyDefinitions/2c89a2e5-7285-40fe-afe0-ae8654b92fab", "allowedLocations": "/providers/Microsoft.Authorization/policyDefinitions/e56962a6-4747-49cd-b67b-bf8b01975c4c", "secureTransferStorage": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "keyVaultDeletionProtection": "/providers/Microsoft.Authorization/policyDefinitions/0b60c0b2-2dc2-4e1c-b5c9-abbed971de53", "storagePublicAccess": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-ewh-naming-convention-initiative", "properties": {"displayName": "Assign EWH Naming Convention Initiative", "description": "Assignment of EWH naming convention initiative to Landing Zone management group", "policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policySetDefinitions', 'ewh-naming-convention-initiative')]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "[parameters('policyEffect')]"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-ewh-tagging-policy", "properties": {"displayName": "Assign EWH Tagging Policy", "description": "Assignment of EWH tagging policy to Landing Zone management group", "policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'ewh-tagging-policy')]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "[parameters('policyEffect')]"}, "requiredTags": {"value": "[parameters('requiredTags')]"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-block-ssh-rdp-custom", "properties": {"displayName": "Block SSH/RDP access from Internet (Custom)", "description": "Assignment of custom policy to block SSH/RDP access from Internet", "policyDefinitionId": "[tenantResourceId('Microsoft.Authorization/policyDefinitions', 'block-ssh-rdp-internet-custom')]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-block-ssh-rdp-builtin", "properties": {"displayName": "Block SSH/RDP access from Internet (Built-in)", "description": "Assignment of built-in policy to block SSH/RDP access from Internet", "policyDefinitionId": "[variables('builtInPolicyIds').blockSshRdpInternet]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-allowed-locations", "properties": {"displayName": "Allowed Locations", "description": "Assignment of allowed locations policy", "policyDefinitionId": "[variables('builtInPolicyIds').allowedLocations]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"listOfAllowedLocations": {"value": "[parameters('allowedLocations')]"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-secure-transfer-storage", "properties": {"displayName": "Secure transfer to storage accounts should be enabled", "description": "Assignment of secure transfer to storage accounts policy", "policyDefinitionId": "[variables('builtInPolicyIds').secureTransferStorage]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "Audit"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-keyvault-deletion-protection", "properties": {"displayName": "Key vaults should have deletion protection enabled", "description": "Assignment of key vault deletion protection policy", "policyDefinitionId": "[variables('builtInPolicyIds').keyVaultDeletionProtection]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "Audit"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "assign-storage-public-access", "properties": {"displayName": "Configure your Storage account public access to be disallowed", "description": "Assignment of storage account public access policy", "policyDefinitionId": "[variables('builtInPolicyIds').storagePublicAccess]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups', variables('mgmtGroups').lzs)]", "parameters": {"effect": {"value": "Modify"}}}}]}